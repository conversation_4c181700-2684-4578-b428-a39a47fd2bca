<script setup lang="ts" name="AppDownload">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useRouter } from "vue-router";
import { getAppDownloadConfig } from "@/service";
import Loading from "@/components/Loading.vue";
import { getQuery, deviceDetector, appDownloader } from "@/utils";
import { envConfig } from "@/utils/env";

// // 渠道枚举
// enum CHANEL_TERMINAL {
//   "GP" = 1,
//   "H5" = 2, // otherApp
//   "iOS" = 4,
//   "Gcash" = 8,
//   "Maya" = 64,
//   "Web" = 128,
// }

// 页面配置数据类型
interface DownloadConfig {
  picture_url: string;
  slogan: string;
  iosButton: {
    ios_switch: boolean;
    text: string;
    storeUrl: string;
  };
  androidButton: {
    android_switch: boolean;
    text: string;
    storeUrl: string;
  };
}

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const config = ref<DownloadConfig | null>(null);
const deviceType = ref(deviceDetector.getDeviceType());
const isDownloading = ref(false);

const hasTwoBtns = computed(() => {
  return config.value?.iosButton.ios_switch && config.value?.androidButton.android_switch;
});

const assetsUrl = (): string => envConfig.assetsUrl;

// 初始化页面数据
async function initPageData() {
  loading.value = true;
  // 尝试请求配置数据，如果失败则使用默认配置
  try {
    const query = getQuery(window.location.search);
    const channel = query.channel;
    const res = await getAppDownloadConfig({ channel_type: channel });
    console.log("尝试请求配置数据，如果失败则使用默认配置", res);
    if (res.code === 0 && res.data) {
      config.value = {
        picture_url: res.data.picture_url ? assetsUrl() + res.data.picture_url : "",
        slogan: res.data.slogan,
        iosButton: {
          ios_switch: res.data.ios_switch,
          text: res.data.iosButtonText || "Download on the iOS",
          storeUrl: res.data.ios_download_url,
        },
        androidButton: {
          android_switch: res.data.android_switch,
          text: res.data.androidButtonText || "Download on the Android",
          storeUrl: res.data.android_download_url,
        },
      };
    } else {
      // API返回成功但数据为空，或者返回错误码，使用默认配置
      config.value = getDefaultConfig();
      console.log("API返回数据为空或错误，使用默认配置", config.value);
    }
  } catch (apiError) {
    console.log("API not available, using default config");
    // 使用默认配置
    config.value = getDefaultConfig();
  } finally {
    loading.value = false;
  }
}

// 获取默认配置
function getDefaultConfig(): DownloadConfig {
  return {
    picture_url: "",
    slogan: `Download the app & claim your free bonus instantly! Daily rewards, big wins`,
    iosButton: {
      ios_switch: false,
      text: "iOS",
      storeUrl: "",
    },
    androidButton: {
      android_switch: false,
      text: "Android",
      storeUrl: "",
    },
  };
}

// 处理下载按钮点击
async function handleDownload(platform: "ios" | "android") {
  if (isDownloading.value || !config.value) return;

  isDownloading.value = true;

  try {
    if (platform === "ios") {
      // 跳转暂不支持页面
      router.push("/notSupported");
      return;
    }
    const buttonConfig = config.value.androidButton;
    if (!buttonConfig.storeUrl) return;

    // 先尝试打开app，失败后跳转应用商城
    const success = await appDownloader.openApp(buttonConfig.storeUrl, platform, 3000);
    if (!success) {
      // app和应用商城都打开失败
      console.error(`Failed to open ${platform} app or store`);
      appDownloader.showToast("Download failed, please try again.");
    } else {
      console.log(`Successfully initiated ${platform} app opening process`);
    }
  } catch (error) {
    console.error("Download error:", error);
    appDownloader.showToast("Download failed, please try again.");
  } finally {
    isDownloading.value = false;
  }
}

// iOS Safari 兼容性：处理触摸滚动事件
function handleTouchMove(e: TouchEvent) {
  // 检查是否在内容区域内
  const target = e.target as HTMLElement;
  const contentArea = target.closest(".content-area");

  if (contentArea) {
    // 允许内容区域滚动
    return;
  }

  // 阻止其他区域的滚动，防止页面整体滚动
  e.preventDefault();
}

// iOS Safari 兼容性：处理视口高度变化
function handleViewportChange() {
  // 强制重新计算视口高度，解决iOS Safari地址栏隐藏/显示问题
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty("--vh", `${vh}px`);
}

// iOS Safari 兼容性：处理页面可见性变化
function handleVisibilityChange() {
  if (!document.hidden) {
    // 页面重新可见时，重新计算视口高度
    setTimeout(handleViewportChange, 100);
  }
}

// 页面挂载时初始化
onMounted(() => {
  initPageData();

  // iOS Safari 兼容性：设置初始视口高度
  handleViewportChange();

  // 添加事件监听器
  document.addEventListener("touchmove", handleTouchMove, { passive: false });
  window.addEventListener("resize", handleViewportChange);
  window.addEventListener("orientationchange", handleViewportChange);
  document.addEventListener("visibilitychange", handleVisibilityChange);

  // iOS Safari 兼容性：延迟执行，确保页面完全加载
  setTimeout(handleViewportChange, 300);
});

onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener("touchmove", handleTouchMove);
  window.removeEventListener("resize", handleViewportChange);
  window.removeEventListener("orientationchange", handleViewportChange);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});
</script>

<template>
  <Loading v-if="loading" />
  <div class="page-wap" v-else>
    <div class="download-page">
      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- <img v-if="config?.picture_url" :src="config?.picture_url" alt="" /> -->
        <img src="@/assets/1.jpg" alt="" />
      </div>

      <!-- 底部固定下载模块 -->
      <div class="download-section">
        <!-- 下载按钮容器 -->
        <div class="download-buttons">
          <!-- iOS 下载按钮 -->
          <button
            class="download-btn ios-btn"
            v-if="config?.iosButton.ios_switch"
            :class="{ downloading: isDownloading }"
            :disabled="isDownloading"
            @click="handleDownload('ios')"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <img src="@/assets/img/download/iOS.png" alt="" />
              </div>
              <div class="btn-text">
                <div class="btn-subtitle">{{ hasTwoBtns ? "iOS" : "Download" }}</div>
              </div>
            </div>
          </button>

          <!-- Android 下载按钮 -->
          <button
            class="download-btn android-btn"
            v-if="config?.androidButton.android_switch"
            :class="{ downloading: isDownloading }"
            :disabled="isDownloading"
            @click="handleDownload('android')"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <img src="@/assets/img/download/Android.png" alt="" />
              </div>
              <div class="btn-text">
                <div class="btn-subtitle">{{ hasTwoBtns ? "Android" : "Download" }}</div>
              </div>
            </div>
          </button>
        </div>

        <!-- 标语文本 -->
        <div class="slogan-text">
          {{ config?.slogan }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-wap {
  /* iOS Safari 兼容性：使用 constant 和 env 双重声明 */
  padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS >= 11.2 */
  overflow: hidden;
  position: relative;
  width: 100%;
  /* iOS Safari 兼容性：多种高度方案 */
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100); /* 使用自定义变量 */
  height: 100dvh; /* 现代浏览器支持 */
}

.download-page {
  font-family: "Inter";
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  /* iOS Safari 兼容性：确保不会出现滚动条 */
  overflow: hidden;
  /* iOS Safari 兼容性：使用 webkit 前缀 */
  -webkit-overflow-scrolling: touch;
}

.content-area {
  /* 关键：使用 calc 计算剩余高度，确保下载区域始终可见 */
  flex: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* iOS Safari 兼容性：平滑滚动 */
  -webkit-overflow-scrolling: touch;
  /* iOS Safari 兼容性：防止橡皮筋效果影响布局 */
  overscroll-behavior: contain;

  img {
    width: 100%;
    height: auto;
    display: block;
    /* iOS Safari 兼容性：确保图片正确显示 */
    object-fit: contain;
    object-position: top center;
    /* 确保长图可以完整显示 */
    min-height: 100%;
    /* iOS Safari 兼容性：防止图片变形 */
    -webkit-user-select: none;
    user-select: none;
  }
}

.download-section {
  /* 关键：固定定位到底部 */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  font-family: "Inter";
  background: #fff;
  width: 100%;
  box-sizing: border-box;

  /* iOS Safari 兼容性：确保在安全区域内显示 */
  padding: 16px 16px calc(20px + constant(safe-area-inset-bottom));
  padding: 16px 16px calc(20px + env(safe-area-inset-bottom));

  /* 内容布局 */
  display: flex;
  flex-direction: column;
  gap: 12px;

  /* iOS Safari 兼容性：防止内容被压缩 */
  flex-shrink: 0;
  min-height: auto;

  /* 添加顶部阴影，增强视觉层次 */
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

  /* iOS Safari 兼容性：确保背景色覆盖完整 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.download-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
  /* iOS Safari 兼容性：确保按钮始终并排，不换行 */
  flex-wrap: nowrap;
  /* iOS Safari 兼容性：防止按钮被压缩 */
  flex-shrink: 0;
}

.download-btn {
  flex: 1;
  background: #222222;
  border-radius: 30px;
  height: 60px;
  color: #fff;
  border: none;
  cursor: pointer;

  /* iOS Safari 兼容性：确保按钮可点击 */
  -webkit-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;

  /* iOS Safari 兼容性：防止按钮变形 */
  min-width: 0;
  box-sizing: border-box;

  /* 按钮状态样式 */
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    background: #333333;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &.downloading {
    opacity: 0.8;
  }
}

.btn-content {
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%;
  /* iOS Safari 兼容性：确保内容居中 */
  box-sizing: border-box;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  /* iOS Safari 兼容性：防止图标变形 */
  flex-shrink: 0;

  > img {
    width: 28px;
    height: 28px;
    /* iOS Safari 兼容性：确保图片正确显示 */
    object-fit: contain;
    -webkit-user-select: none;
    user-select: none;
  }
}

.btn-text {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
  /* iOS Safari 兼容性：防止文本被压缩 */
  flex-shrink: 0;
}

.btn-subtitle {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  color: #fff;
  /* iOS Safari 兼容性：防止文本换行 */
  white-space: nowrap;
  /* iOS Safari 兼容性：确保文本渲染清晰 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slogan-text {
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  color: #666;
  word-wrap: break-word;
  overflow: hidden;

  /* iOS Safari 兼容性：使用 webkit 前缀 */
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制显示3行 */
  -webkit-box-orient: vertical;

  /* 设置最大高度为3行的高度 */
  max-height: calc(20px * 3); /* line-height * 3 */

  /* iOS Safari 兼容性：确保文本渲染清晰 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* iOS Safari 兼容性：防止内容被压缩 */
  flex-shrink: 0;
}

/* iOS Safari 兼容性：添加媒体查询处理特殊情况 */
@media screen and (max-height: 600px) {
  .download-section {
    padding: 12px 16px calc(16px + constant(safe-area-inset-bottom));
    padding: 12px 16px calc(16px + env(safe-area-inset-bottom));
    gap: 8px;
  }

  .download-btn {
    height: 50px;
  }

  .btn-subtitle {
    font-size: 14px;
    line-height: 20px;
  }

  .slogan-text {
    font-size: 12px;
    line-height: 18px;
    -webkit-line-clamp: 2;
    max-height: calc(18px * 2);
  }
}

/* iOS Safari 兼容性：处理横屏模式 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .download-section {
    padding: 8px 16px calc(12px + constant(safe-area-inset-bottom));
    padding: 8px 16px calc(12px + env(safe-area-inset-bottom));
    gap: 6px;
  }

  .download-btn {
    height: 44px;
  }

  .btn-subtitle {
    font-size: 14px;
    line-height: 18px;
  }

  .slogan-text {
    font-size: 11px;
    line-height: 16px;
    -webkit-line-clamp: 2;
    max-height: calc(16px * 2);
  }
}
</style>
