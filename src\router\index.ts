import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: "home",
      path: "/",
      component: () => import("../views/PayResult/index.vue"),
    },
    {
      name: "cocosPayResult",
      path: "/cocos/payResult",
      component: () => import("../views/PayResult/index.vue"),
    },
    {
      name: "vueH5PayResult",
      path: "/vueH5/payResult",
      component: () => import("../views/PayResult/index.vue"),
    },
    {
      name: "download",
      path: "/download",
      component: () => import("../views/DownloadPage/index.vue"),
    },
    {
      name: "download1",
      path: "/download1",
      component: () => import("../views/DownloadPage/index1.vue"),
    },
    {
      name: "notSupported",
      path: "/notSupported",
      component: () => import("../views/DownloadPage/notSupported.vue"),
    },
  ],
});

export default router;
