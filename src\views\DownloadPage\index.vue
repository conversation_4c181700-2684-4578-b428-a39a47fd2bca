<script setup lang="ts" name="AppDownload">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { getAppDownloadConfig } from "@/service";
import Loading from "@/components/Loading.vue";
import { getQuery, deviceDetector, appDownloader } from "@/utils";
import { envConfig } from "@/utils/env";

// // 渠道枚举
// enum CHANEL_TERMINAL {
//   "GP" = 1,
//   "H5" = 2, // otherApp
//   "iOS" = 4,
//   "Gcash" = 8,
//   "Maya" = 64,
//   "Web" = 128,
// }

// 页面配置数据类型
interface DownloadConfig {
  picture_url: string;
  slogan: string;
  iosButton: {
    ios_switch: boolean;
    text: string;
    storeUrl: string;
  };
  androidButton: {
    android_switch: boolean;
    text: string;
    storeUrl: string;
  };
}

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const config = ref<DownloadConfig | null>(null);
const deviceType = ref(deviceDetector.getDeviceType());
const isDownloading = ref(false);

const hasTwoBtns = computed(() => {
  return config.value?.iosButton.ios_switch && config.value?.androidButton.android_switch;
});

const assetsUrl = (): string => envConfig.assetsUrl;

// 初始化页面数据
async function initPageData() {
  loading.value = true;
  // 尝试请求配置数据，如果失败则使用默认配置
  try {
    const query = getQuery(window.location.search);
    const channel = query.channel;
    const res = await getAppDownloadConfig({ channel_type: channel });
    console.log("尝试请求配置数据，如果失败则使用默认配置", res);
    if (res.code === 0 && res.data) {
      config.value = {
        picture_url: res.data.picture_url ? assetsUrl() + res.data.picture_url : "",
        slogan: res.data.slogan,
        iosButton: {
          ios_switch: res.data.ios_switch,
          text: res.data.iosButtonText || "Download on the iOS",
          storeUrl: res.data.ios_download_url,
        },
        androidButton: {
          android_switch: res.data.android_switch,
          text: res.data.androidButtonText || "Download on the Android",
          storeUrl: res.data.android_download_url,
        },
      };
    } else {
      // API返回成功但数据为空，或者返回错误码，使用默认配置
      config.value = getDefaultConfig();
      console.log("API返回数据为空或错误，使用默认配置", config.value);
    }
  } catch (apiError) {
    console.log("API not available, using default config");
    // 使用默认配置
    config.value = getDefaultConfig();
  } finally {
    loading.value = false;
  }
}

// 获取默认配置
function getDefaultConfig(): DownloadConfig {
  return {
    picture_url: "",
    slogan: `Download the app & claim your free bonus instantly! Daily rewards, big wins`,
    iosButton: {
      ios_switch: false,
      text: "iOS",
      storeUrl: "",
    },
    androidButton: {
      android_switch: false,
      text: "Android",
      storeUrl: "",
    },
  };
}

// 处理下载按钮点击
async function handleDownload(platform: "ios" | "android") {
  if (isDownloading.value || !config.value) return;

  isDownloading.value = true;

  try {
    if (platform === "ios") {
      // 跳转暂不支持页面
      router.push("/notSupported");
      return;
    }
    const buttonConfig = config.value.androidButton;
    if (!buttonConfig.storeUrl) return;

    // 先尝试打开app，失败后跳转应用商城
    const success = await appDownloader.openApp(buttonConfig.storeUrl, platform, 3000);
    if (!success) {
      // app和应用商城都打开失败
      console.error(`Failed to open ${platform} app or store`);
      appDownloader.showToast("Download failed, please try again.");
    } else {
      console.log(`Successfully initiated ${platform} app opening process`);
    }
  } catch (error) {
    console.error("Download error:", error);
    appDownloader.showToast("Download failed, please try again.");
  } finally {
    isDownloading.value = false;
  }
}

// 页面挂载时初始化
onMounted(() => {
  initPageData();
  function getViewportSize() {
    return {
      width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),
      height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0),
    };
  }

  // 使用示例
  const size = getViewportSize();
  console.log(`视口尺寸: ${size.width}x${size.height}`);
});
</script>

<template>
  <Loading v-if="loading" />
  <div class="page-wap" v-else>
    <div class="download-page">
      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- <img v-if="config?.picture_url" :src="config?.picture_url" alt="" /> -->
        <img src="@/assets/1.jpg" alt="" />
      </div>

      <!-- 底部固定下载模块 -->
      <div class="download-section">
        <!-- 下载按钮容器 -->
        <div class="download-buttons">
          <!-- iOS 下载按钮 -->
          <button
            class="download-btn ios-btn"
            v-if="config?.iosButton.ios_switch"
            :class="{ downloading: isDownloading }"
            :disabled="isDownloading"
            @click="handleDownload('ios')"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <img src="@/assets/img/download/iOS.png" alt="" />
              </div>
              <div class="btn-text">
                <div class="btn-subtitle">{{ hasTwoBtns ? "iOS" : "Download" }}</div>
              </div>
            </div>
          </button>

          <!-- Android 下载按钮 -->
          <button
            class="download-btn android-btn"
            v-if="config?.androidButton.android_switch"
            :class="{ downloading: isDownloading }"
            :disabled="isDownloading"
            @click="handleDownload('android')"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <img src="@/assets/img/download/Android.png" alt="" />
              </div>
              <div class="btn-text">
                <div class="btn-subtitle">{{ hasTwoBtns ? "Android" : "Download" }}</div>
              </div>
            </div>
          </button>
        </div>

        <!-- 标语文本 -->
        <div class="slogan-text">
          {{ config?.slogan }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-wap {
  padding-bottom: env(safe-area-inset-bottom); /* 预估下载区域高度 + 安全区域 */
  padding-bottom: constant(safe-area-inset-bottom);
  /* 动态计算 padding，考虑 iOS Safari 地址栏和安全区域 */
  /*  padding: 0 0 calc(20px + env(safe-area-inset-bottom));
  padding: 0 0 calc(20px + constant(safe-area-inset-bottom)); */
  overflow: hidden;
}
.download-page {
  font-family: "Inter";
  height: 100%;
  // height: 100vh;
  // height: 100dvh; /* 动态视口高度，避免iOS Safari地址栏影响 */
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.content-area {
  flex: 1;
  height: 100dvh;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 160px;

  img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain; /* 改为 contain 以完整显示长图 */
    min-height: 100%; /* 确保图片至少填满容器高度 */
  }
}

.download-section {
  font-family: "Inter";
  position: fixed; /* 改为固定定位，确保始终在底部 */
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 333;
  background: #fff;
  max-height: 160px;
  padding: 18px;
  width: 100%;
  gap: 12px;
  flex-shrink: 0;
  box-sizing: border-box;
  /* 添加阴影以区分内容区域 */
  box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.download-buttons {
  display: flex;
  gap: 12px;
  // 确保按钮始终并排，不换行
  flex-wrap: nowrap;
  width: 100%;
}

.download-btn {
  flex: 1;
  background: #222222;
  border-radius: 30px;
  width: 163px;
  height: 60px;
  color: #fff;
}

.btn-content {
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  > img {
    width: 28px;
    height: 28px;
  }
}
.btn-text {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
}

.btn-subtitle {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  color: #fff;
}

.slogan-text {
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  color: #666;
  word-wrap: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 增加到4行以适应更多内容 */
  -webkit-box-orient: vertical;
  /* 设置最大高度为4行的高度 */
  max-height: calc(20px * 3); /* line-height * 4 */
  /* 当内容较少时自动收缩 */
  min-height: 20px; /* 至少一行高度 */
}

// /* 针对不同按钮数量的适配 */
// .download-section:has(.download-buttons .download-btn:only-child) {
//   /* 只有一个按钮时的样式调整 */
//   min-height: calc(
//     60px + 32px + 20px + env(safe-area-inset-bottom)
//   ); /* 按钮高度 + padding + 文本行高 + 安全区域 */
//   min-height: calc(60px + 32px + 20px + constant(safe-area-inset-bottom));
// }

// .download-section:has(.download-buttons .download-btn:nth-child(2)) {
//   /* 有两个按钮时的样式调整 */
//   min-height: calc(
//     60px + 32px + 80px + env(safe-area-inset-bottom)
//   ); /* 按钮高度 + padding + 更多文本空间 + 安全区域 */
//   min-height: calc(60px + 32px + 80px + constant(safe-area-inset-bottom));
// }
</style>
