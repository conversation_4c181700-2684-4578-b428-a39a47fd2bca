<script lang="ts" setup name="App">
import { RouterView } from "vue-router";
import { envConfig } from "@/utils/env";

// 仅在开发环境和测试环境启用 vConsole
if (envConfig.enableVConsole) {
  import("vconsole").then((vConsole) => {
    new vConsole.default();
  });
}
</script>

<template>
  <div class="app">
    <RouterView />
  </div>
</template>

<style lang="scss">
.app {
  width: 100%;
  overflow: hidden;
}
</style>
